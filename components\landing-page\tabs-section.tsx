"use client";

import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { useState, useRef, useEffect } from "react";
import { Play, Pause, ChevronRightIcon } from "lucide-react";
import { Tabs, Ta<PERSON>List, TabsTrigger, TabsContent } from "../ui/tabs";
import { gsap } from "gsap";

export function TabsSection() {
  const [activeTab, setActiveTab] = useState("sketching");
  const videoRefs = useRef<{ [key: string]: HTMLVideoElement | null }>({});
  const [playingStates, setPlayingStates] = useState<{ [key: string]: boolean }>({
    sketching: false,
    modeling: false,
    visualization: false,
  });

  // Animation refs
  const sectionRef = useRef<HTMLElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const tabsListRef = useRef<HTMLDivElement>(null);
  const tabContentRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const tabTriggerRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});
  const [isInitialized, setIsInitialized] = useState(false);

  const tabsData = [
    {
      id: "sketching",
      label: "Sketching",
      badge: "Sketch and Draw",
      title: "Sketch with precision or go freestyle",
      description:
        "Use the Precision tool for straight lines and arcs, the Pen tool for smooth curves, or go freehand with the Freestyle tool. Sketch exactly how you want.",
      buttonText: "Explore Sketching",
      vidSrc: "./tab-1.mp4",
    },
    {
      id: "modeling",
      label: "Modeling",
      badge: "Advanced Modeling Tools",
      title: "Add depth with 3D modeling",
      description:
        "Create 3D models with ease using advanced features like Push & Pull, NURBS surfaces, or simply drag and drop elements from the library.",
      buttonText: "Explore Modeling",
      vidSrc: "./tab-2.mp4",
    },
    {
      id: "visualization",
      label: "Visualization",
      badge: "Showcase Your Work",
      title: "Visualize your ideas in the best light",
      description:
        "Showcase your designs with on-device ray-tracing for realistic rendering, or enhance presentations with Slides using Markup tools.",
      buttonText: "Explore Visualization",
      vidSrc: "./tab-3.mp4",
    },
  ];

  // Animation functions
  const initializeAnimations = () => {
    if (!sectionRef.current || isInitialized) return;

    // Set initial states
    gsap.set([headerRef.current, tabsListRef.current], {
      opacity: 0,
      y: 30,
    });

    // Set initial state for all tab content
    Object.values(tabContentRefs.current).forEach((ref) => {
      if (ref) {
        gsap.set(ref, { opacity: 0 });
        const elements = ref.querySelectorAll(
          ".tab-badge, .tab-title, .tab-description, .tab-button",
        );
        const videoContainer = ref.querySelector(".tab-video-container");
        gsap.set(elements, { opacity: 0, y: 30 });
        if (videoContainer) {
          gsap.set(videoContainer, { opacity: 0, scale: 0.9 });
        }
      }
    });

    // Initial load animation timeline
    const tl = gsap.timeline();

    // Animate header elements with stagger
    const headerElements = headerRef.current?.children;
    if (headerElements) {
      gsap.set(headerElements, { opacity: 0, y: 30 });
      tl.to(headerElements, {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: "power2.out",
      });
    }

    tl.to(
      tabsListRef.current,
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out",
      },
      "-=0.3",
    );

    // Animate initial tab content
    const activeContent = tabContentRefs.current[activeTab];
    if (activeContent) {
      const elements = activeContent.querySelectorAll(
        ".tab-badge, .tab-title, .tab-description, .tab-button",
      );
      const videoContainer = activeContent.querySelector(".tab-video-container");

      // Set initial state for video
      if (videoContainer) {
        gsap.set(videoContainer, { opacity: 0, scale: 0.9 });
      }

      tl.set(activeContent, { opacity: 1 }, "-=0.2").to(
        elements,
        {
          opacity: 1,
          y: 0,
          duration: 0.5,
          stagger: 0.1,
          ease: "power2.out",
        },
        "-=0.1",
      );

      // Animate video container
      if (videoContainer) {
        tl.to(
          videoContainer,
          {
            opacity: 1,
            scale: 1,
            duration: 0.6,
            ease: "back.out(1.7)",
          },
          "-=0.3",
        );
      }
    }

    setIsInitialized(true);
  };

  const animateTabTransition = (fromTab: string, toTab: string) => {
    const fromContent = tabContentRefs.current[fromTab];
    const toContent = tabContentRefs.current[toTab];

    if (!fromContent || !toContent) return;

    const tl = gsap.timeline();

    // Get content elements for detailed animations
    const fromElements = fromContent.querySelectorAll(
      ".tab-badge, .tab-title, .tab-description, .tab-button",
    );
    const toElements = toContent.querySelectorAll(
      ".tab-badge, .tab-title, .tab-description, .tab-button",
    );
    const fromVideo = fromContent.querySelector(".tab-video-container");
    const toVideo = toContent.querySelector(".tab-video-container");

    // Animate out current content with stagger
    tl.to(fromElements, {
      opacity: 0,
      y: -15,
      duration: 0.25,
      stagger: 0.05,
      ease: "power2.in",
    });

    // Animate out video with scale and fade
    if (fromVideo) {
      tl.to(
        fromVideo,
        {
          opacity: 0,
          scale: 0.95,
          duration: 0.3,
          ease: "power2.in",
        },
        "-=0.2",
      );
    }

    tl.to(
      fromContent,
      {
        opacity: 0,
        duration: 0.1,
      },
      "-=0.1",
    )
      // Set initial state for new content elements
      .set(toElements, {
        opacity: 0,
        y: 25,
      })
      .set(toContent, {
        opacity: 1,
      });

    // Set initial state for new video
    if (toVideo) {
      tl.set(toVideo, {
        opacity: 0,
        scale: 0.95,
      });
    }

    // Animate in new content with stagger
    tl.to(
      toElements,
      {
        opacity: 1,
        y: 0,
        duration: 0.4,
        stagger: 0.08,
        ease: "power2.out",
      },
      "-=0.05",
    );

    // Animate in new video
    if (toVideo) {
      tl.to(
        toVideo,
        {
          opacity: 1,
          scale: 1,
          duration: 0.5,
          ease: "back.out(1.7)",
        },
        "-=0.3",
      );
    }
  };

  const addHoverAnimations = () => {
    Object.entries(tabTriggerRefs.current).forEach(([tabId, ref]) => {
      if (!ref) return;

      const handleMouseEnter = () => {
        if (activeTab !== tabId) {
          gsap.to(ref, {
            scale: 1.02,
            y: -2,
            duration: 0.3,
            ease: "power2.out",
          });
        }
      };

      const handleMouseLeave = () => {
        if (activeTab !== tabId) {
          gsap.to(ref, {
            scale: 1,
            y: 0,
            duration: 0.3,
            ease: "power2.out",
          });
        }
      };

      ref.addEventListener("mouseenter", handleMouseEnter);
      ref.addEventListener("mouseleave", handleMouseLeave);

      // Cleanup function stored on the element
      (ref as any)._cleanupHover = () => {
        ref.removeEventListener("mouseenter", handleMouseEnter);
        ref.removeEventListener("mouseleave", handleMouseLeave);
      };
    });

    // Add hover animations for buttons
    Object.values(tabContentRefs.current).forEach((content) => {
      if (!content) return;

      const button = content.querySelector(".tab-button");
      if (!button) return;

      const handleButtonEnter = () => {
        gsap.to(button, {
          scale: 1.05,
          duration: 0.2,
          ease: "power2.out",
        });
      };

      const handleButtonLeave = () => {
        gsap.to(button, {
          scale: 1,
          duration: 0.2,
          ease: "power2.out",
        });
      };

      button.addEventListener("mouseenter", handleButtonEnter);
      button.addEventListener("mouseleave", handleButtonLeave);

      // Store cleanup function
      (button as any)._cleanupHover = () => {
        button.removeEventListener("mouseenter", handleButtonEnter);
        button.removeEventListener("mouseleave", handleButtonLeave);
      };
    });
  };

  const handleVideoLoad = (tabId: string) => {
    const video = videoRefs.current[tabId];
    if (video) {
      video.play().catch((error) => {
        console.log("Auto-play prevented:", error);
      });
    }
  };

  const handlePlayPause = (tabId: string) => {
    const video = videoRefs.current[tabId];
    if (video) {
      if (video.paused) {
        video.play();
      } else {
        video.pause();
      }
    }
  };

  const handleVideoPlay = (tabId: string) => {
    setPlayingStates((prev) => ({ ...prev, [tabId]: true }));
  };

  const handleVideoPause = (tabId: string) => {
    setPlayingStates((prev) => ({ ...prev, [tabId]: false }));
  };

  // Initialize animations on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      initializeAnimations();
      addHoverAnimations();
    }, 100); // Small delay to ensure DOM is ready

    return () => {
      clearTimeout(timer);
      // Cleanup hover animations
      Object.values(tabTriggerRefs.current).forEach((ref) => {
        if (ref && (ref as any)._cleanupHover) {
          (ref as any)._cleanupHover();
        }
      });
    };
  }, []);

  // Handle tab changes with animations
  useEffect(() => {
    const currentVideo = videoRefs.current[activeTab];
    if (currentVideo) {
      currentVideo.play().catch((error) => {
        console.log("Auto-play prevented:", error);
      });
    }

    Object.keys(videoRefs.current).forEach((tabId) => {
      if (tabId !== activeTab) {
        const video = videoRefs.current[tabId];
        if (video) {
          video.pause();
        }
      }
    });
  }, [activeTab]);

  // Handle tab transition animations
  const handleTabChange = (newTab: string) => {
    if (newTab !== activeTab && isInitialized) {
      animateTabTransition(activeTab, newTab);
    }
    setActiveTab(newTab);
  };

  return (
    <section ref={sectionRef} className="mx-auto max-w-6xl px-4 pb-40 sm:px-5">
      <div ref={headerRef} className="space-y-4 text-center">
        <Badge variant="outline" className="px-4 py-2 text-sm font-semibold">
          All-in-One Design Tools
        </Badge>
        <h1 className="font-instrument-serif text-5xl leading-tight md:text-6xl">
          3D design made simple
        </h1>
        <p className="text-muted-foreground mx-auto max-w-xl text-lg">
          Easily sketch, model, and explore a rich library of 3D models, textures, and more to bring
          your ideas to life in 3D.
        </p>
      </div>
      <div className="pt-10">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <div ref={tabsListRef} className="flex justify-center pb-14">
            <TabsList className="bg-secondary grid h-14 w-fit grid-cols-3 p-1">
              {tabsData.map((tab) => (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  ref={(el) => {
                    tabTriggerRefs.current[tab.id] = el;
                  }}
                  className="data-[state=active]:bg-background px-6 py-3 text-sm font-medium transition-transform duration-200 data-[state=active]:shadow-sm"
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
          {tabsData.map((tab) => (
            <TabsContent
              key={tab.id}
              value={tab.id}
              className="mt-0"
              ref={(el) => {
                tabContentRefs.current[tab.id] = el;
              }}
            >
              <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-12">
                <div className="space-y-6 lg:order-1">
                  <div className="space-y-4">
                    <Badge
                      variant="outline"
                      className="tab-badge px-4 py-2 text-sm font-semibold"
                      data-tab={tab.id}
                    >
                      {tab.badge}
                    </Badge>
                    <h2
                      className="font-instrument-serif tab-title text-4xl leading-tight font-semibold md:text-5xl lg:text-6xl"
                      data-tab={tab.id}
                    >
                      {tab.title}
                    </h2>
                    <p
                      className="text-muted-foreground tab-description text-lg leading-relaxed"
                      data-tab={tab.id}
                    >
                      {tab.description}
                    </p>
                  </div>
                  <Button
                    className="tab-button !h-11 w-full rounded-lg !px-4 text-base font-medium sm:w-auto"
                    data-tab={tab.id}
                  >
                    {tab.buttonText}
                    <ChevronRightIcon className="mt-0.5 size-5" />
                  </Button>
                </div>
                <div className="lg:order-2">
                  <div className="relative mx-auto max-w-lg">
                    <div
                      className="from-foreground/5 via-foreground/10 to-foreground/5 tab-video-container relative overflow-hidden rounded-2xl bg-gradient-to-br shadow-xl"
                      data-tab={tab.id}
                    >
                      <div className="relative aspect-[4/3]">
                        <video
                          ref={(el) => {
                            videoRefs.current[tab.id] = el;
                          }}
                          className="h-full w-full rounded-2xl object-cover"
                          onLoadedData={() => handleVideoLoad(tab.id)}
                          onPlay={() => handleVideoPlay(tab.id)}
                          onPause={() => handleVideoPause(tab.id)}
                          onPlaying={() => handleVideoPlay(tab.id)}
                          onWaiting={() => handleVideoPause(tab.id)}
                          onEnded={() => handleVideoPause(tab.id)}
                          autoPlay={activeTab === tab.id}
                          loop
                          playsInline
                          preload="metadata"
                          muted
                        >
                          <source src={tab.vidSrc} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                        <div className="absolute bottom-4 left-4 flex items-center">
                          <button
                            onClick={() => handlePlayPause(tab.id)}
                            className="flex h-10 w-10 cursor-pointer items-center justify-center rounded-full border border-white/30 bg-white/20 backdrop-blur-md transition-all duration-300 hover:bg-white/30"
                            aria-label={playingStates[tab.id] ? "Pause video" : "Play video"}
                          >
                            {playingStates[tab.id] ? (
                              <Pause className="h-4 w-4 text-white" />
                            ) : (
                              <Play className="ml-0.5 h-4 w-4 text-white" />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
}
